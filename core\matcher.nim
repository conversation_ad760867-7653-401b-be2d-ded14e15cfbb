import json, strutils, sequtils, re, options, asyncdispatch
import bot

type
  RuleFunc* = proc(event: JsonNode): bool {.gcsafe.}
  HandlerFunc* = proc(bot: Bot, event: JsonNode): Future[void] {.gcsafe.}

  MatcherOptions* = object
    priority*: int
    `block`*: bool
    temp*: bool
    forceWhitespace*: Option[bool]
    splitBy*: string
    aliases*: seq[string]

  Matcher* = ref object
    eventType*: string
    rule*: RuleFunc
    handler*: HandlerFunc
    priority*: int
    `block`*: bool
    temp*: bool

# 全局匹配器列表，用于插件注册
var globalMatchers*: seq[Matcher] = @[]

proc addGlobalMatcher*(m: Matcher) =
  globalMatchers.add(m)

proc newMatcher*(eventType: string, rule: RuleFunc, handler: HandlerFunc,
    options: MatcherOptions = MatcherOptions()): Matcher =
  let m = Matcher(
    eventType: eventType,
    rule: rule,
    handler: handler,
    priority: if options.priority == 0: 1 else: options.priority,
    `block`: options.`block`,
  )
  addGlobalMatcher(m)
  m

proc onMessage*(rule: RuleFunc, handler: HandlerFunc,
    options: MatcherOptions = MatcherOptions()): Matcher =
  newMatcher("message", rule, handler, options)

proc onCommand*(cmd: string, handler: HandlerFunc,
    options: MatcherOptions = MatcherOptions()): Matcher =
  let commands = @[cmd] & options.aliases

  let rule = proc(event: JsonNode): bool =
    let msg = event{"raw_message"}.getStr("")
    if msg.len == 0: return false

    for command in commands:
      let pattern =
        if options.forceWhitespace.isSome:
          if options.forceWhitespace.get:
            "^" & command & r"\s"
          else:
            "^" & command
        else:
          "^" & command & r"(\s|$)"

      if msg.match(re(pattern)): return true
    false

  var opts = options
  if not opts.`block`: opts.`block` = true
  onMessage(rule, handler, opts)

proc onKeyword*(keywords: seq[string], handler: HandlerFunc,
    options: MatcherOptions = MatcherOptions()): Matcher =
  let rule = proc(event: JsonNode): bool =
    let msg = event{"raw_message"}.getStr("")
    keywords.anyIt(msg.contains(it))

  onMessage(rule, handler, options)





